/**
 * Simple API contract test runner
 * Run with: node scripts/test-api-contract.js
 */

const API_BASE_URL = 'https://ai-nav.onrender.com';

// Test payloads to understand the API contract
const fieldTests = [
  {
    name: 'Empty tool_details',
    payload: {
      name: 'Test Tool 1',
      description: 'Test description',
      website_url: 'https://example.com',
      entity_type_id: 'test-id',
      tool_details: {}
    }
  },
  {
    name: 'Only has_free_tier',
    payload: {
      name: 'Test Tool 2',
      description: 'Test description',
      website_url: 'https://example.com',
      entity_type_id: 'test-id',
      tool_details: { has_free_tier: true }
    }
  },
  {
    name: 'Only technical_level',
    payload: {
      name: 'Test Tool 3',
      description: 'Test description',
      website_url: 'https://example.com',
      entity_type_id: 'test-id',
      tool_details: { technical_level: 'INTERMEDIATE' }
    }
  },
  {
    name: 'Only has_api',
    payload: {
      name: 'Test Tool 4',
      description: 'Test description',
      website_url: 'https://example.com',
      entity_type_id: 'test-id',
      tool_details: { has_api: true }
    }
  },
  {
    name: 'Common fields only',
    payload: {
      name: 'Test Tool 5',
      description: 'Test description',
      website_url: 'https://example.com',
      entity_type_id: 'test-id',
      tool_details: {
        pricing_details: 'Free tier available',
        key_features: ['Feature 1', 'Feature 2'],
        use_cases: ['Use case 1']
      }
    }
  }
];

async function testApiContract() {
  console.log('🧪 Testing API Contract for Entity Creation\n');
  
  // Get auth token from environment or prompt user
  const authToken = process.env.TEST_AUTH_TOKEN;
  
  if (!authToken) {
    console.log('❌ Please set TEST_AUTH_TOKEN environment variable with a valid auth token');
    console.log('   You can get this from your browser\'s developer tools when logged in');
    return;
  }

  for (const test of fieldTests) {
    console.log(`🔍 Testing: ${test.name}`);
    console.log(`📤 Payload:`, JSON.stringify(test.payload, null, 2));
    
    try {
      const response = await fetch(`${API_BASE_URL}/entities`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`,
        },
        body: JSON.stringify(test.payload),
      });

      const responseText = await response.text();
      
      if (response.ok) {
        console.log(`✅ Success (${response.status}):`, responseText);
      } else {
        console.log(`❌ Error (${response.status}):`, responseText);
      }
    } catch (error) {
      console.log(`❌ Network Error:`, error.message);
    }
    
    console.log('---\n');
  }
  
  console.log('🏁 Test completed');
  console.log('\n💡 Based on the errors above, we can determine:');
  console.log('   - Which fields are accepted in tool_details');
  console.log('   - Which fields should be added to the backend schema');
  console.log('   - The correct structure for our frontend form');
}

// Run the test
testApiContract().catch(console.error);
