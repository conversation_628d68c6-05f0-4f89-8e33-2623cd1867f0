'use client';

import React from 'react';
import { UseFormRegister, FieldErrors } from 'react-hook-form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';

interface AgencyDetailsFormProps {
  register: UseFormRegister<any>;
  errors: FieldErrors<any>;
  setValue: any;
  watch: any;
}

export default function AgencyDetailsForm({ register, errors, setValue, watch }: AgencyDetailsFormProps) {
  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-gray-900">Agency Details</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Portfolio URL */}
        <div>
          <Label htmlFor="portfolio_url">Portfolio URL</Label>
          <Input
            id="portfolio_url"
            type="url"
            {...register('details.portfolio_url')}
            placeholder="https://agency.com/portfolio"
            className="mt-1"
          />
        </div>

        {/* Team Size */}
        <div>
          <Label htmlFor="team_size">Team Size</Label>
          <Input
            id="team_size"
            {...register('details.team_size')}
            placeholder="10-50 employees"
            className="mt-1"
          />
        </div>

        {/* Contact Email */}
        <div>
          <Label htmlFor="contact_email">Contact Email</Label>
          <Input
            id="contact_email"
            type="email"
            {...register('details.contact_email')}
            placeholder="<EMAIL>"
            className="mt-1"
          />
        </div>

        {/* Region Served */}
        <div>
          <Label htmlFor="region_served">Region Served</Label>
          <Input
            id="region_served"
            {...register('details.region_served')}
            placeholder="North America, Europe, Global..."
            className="mt-1"
          />
        </div>
      </div>

      {/* Services Offered */}
      <div>
        <Label htmlFor="services_offered">Services Offered (comma-separated)</Label>
        <Textarea
          id="services_offered"
          {...register('details.services_offered_text')}
          placeholder="Web development, Mobile apps, AI consulting, Digital marketing..."
          className="mt-1 min-h-[80px]"
        />
        <p className="text-xs text-gray-500 mt-1">
          Enter services separated by commas. They will be converted to a list.
        </p>
      </div>

      {/* Specializations */}
      <div>
        <Label htmlFor="specializations">Specializations (comma-separated)</Label>
        <Textarea
          id="specializations"
          {...register('details.specializations_text')}
          placeholder="E-commerce, SaaS, Healthcare, Fintech..."
          className="mt-1 min-h-[80px]"
        />
        <p className="text-xs text-gray-500 mt-1">
          Enter specializations separated by commas. They will be converted to a list.
        </p>
      </div>
    </div>
  );
}
