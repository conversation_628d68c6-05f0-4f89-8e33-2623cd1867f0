'use client';

import React from 'react';
import { UseFormRegister, FieldErrors } from 'react-hook-form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';

interface ToolDetailsFormProps {
  register: UseFormRegister<any>;
  errors: FieldErrors<any>;
  setValue: any;
  watch: any;
}

export default function ToolDetailsForm({ register, errors, setValue, watch }: ToolDetailsFormProps) {
  const hasApi = watch('details.has_api');
  const hasFreeTier = watch('details.has_free_tier');

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-gray-900">AI Tool Details</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Technical Level */}
        <div>
          <Label htmlFor="technical_level">Technical Level</Label>
          <Select onValueChange={(value) => setValue('details.technical_level', value)}>
            <SelectTrigger className="mt-1">
              <SelectValue placeholder="Select technical level..." />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="BEGINNER">Beginner</SelectItem>
              <SelectItem value="INTERMEDIATE">Intermediate</SelectItem>
              <SelectItem value="ADVANCED">Advanced</SelectItem>
              <SelectItem value="EXPERT">Expert</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Learning Curve */}
        <div>
          <Label htmlFor="learning_curve">Learning Curve</Label>
          <Select onValueChange={(value) => setValue('details.learning_curve', value)}>
            <SelectTrigger className="mt-1">
              <SelectValue placeholder="Select learning curve..." />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="LOW">Low</SelectItem>
              <SelectItem value="MEDIUM">Medium</SelectItem>
              <SelectItem value="HIGH">High</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Pricing Model */}
        <div>
          <Label htmlFor="pricing_model">Pricing Model</Label>
          <Select onValueChange={(value) => setValue('details.pricing_model', value)}>
            <SelectTrigger className="mt-1">
              <SelectValue placeholder="Select pricing model..." />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="FREE">Free</SelectItem>
              <SelectItem value="FREEMIUM">Freemium</SelectItem>
              <SelectItem value="PAID">Paid</SelectItem>
              <SelectItem value="SUBSCRIPTION">Subscription</SelectItem>
              <SelectItem value="ONE_TIME">One-time Purchase</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Price Range */}
        <div>
          <Label htmlFor="price_range">Price Range</Label>
          <Select onValueChange={(value) => setValue('details.price_range', value)}>
            <SelectTrigger className="mt-1">
              <SelectValue placeholder="Select price range..." />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="FREE">Free</SelectItem>
              <SelectItem value="LOW">Low ($1-$50/month)</SelectItem>
              <SelectItem value="MEDIUM">Medium ($51-$200/month)</SelectItem>
              <SelectItem value="HIGH">High ($201-$1000/month)</SelectItem>
              <SelectItem value="ENTERPRISE">Enterprise ($1000+/month)</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Boolean Options */}
      <div className="space-y-4">
        <div className="flex items-center space-x-2">
          <Checkbox
            id="has_api"
            checked={hasApi}
            onCheckedChange={(checked) => setValue('details.has_api', checked)}
          />
          <Label htmlFor="has_api">Has API Access</Label>
        </div>

        <div className="flex items-center space-x-2">
          <Checkbox
            id="has_free_tier"
            checked={hasFreeTier}
            onCheckedChange={(checked) => setValue('details.has_free_tier', checked)}
          />
          <Label htmlFor="has_free_tier">Has Free Tier</Label>
        </div>

        <div className="flex items-center space-x-2">
          <Checkbox
            id="open_source"
            onCheckedChange={(checked) => setValue('details.open_source', checked)}
          />
          <Label htmlFor="open_source">Open Source</Label>
        </div>

        <div className="flex items-center space-x-2">
          <Checkbox
            id="mobile_support"
            onCheckedChange={(checked) => setValue('details.mobile_support', checked)}
          />
          <Label htmlFor="mobile_support">Mobile Support</Label>
        </div>

        <div className="flex items-center space-x-2">
          <Checkbox
            id="trial_available"
            onCheckedChange={(checked) => setValue('details.trial_available', checked)}
          />
          <Label htmlFor="trial_available">Trial Available</Label>
        </div>

        <div className="flex items-center space-x-2">
          <Checkbox
            id="demo_available"
            onCheckedChange={(checked) => setValue('details.demo_available', checked)}
          />
          <Label htmlFor="demo_available">Demo Available</Label>
        </div>
      </div>

      {/* Text Fields */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <Label htmlFor="pricing_url">Pricing URL</Label>
          <Input
            id="pricing_url"
            type="url"
            {...register('details.pricing_url')}
            placeholder="https://example.com/pricing"
            className="mt-1"
          />
        </div>

        <div>
          <Label htmlFor="support_email">Support Email</Label>
          <Input
            id="support_email"
            type="email"
            {...register('details.support_email')}
            placeholder="<EMAIL>"
            className="mt-1"
          />
        </div>

        <div>
          <Label htmlFor="api_documentation_url">API Documentation URL</Label>
          <Input
            id="api_documentation_url"
            type="url"
            {...register('details.api_documentation_url')}
            placeholder="https://docs.example.com/api"
            className="mt-1"
          />
        </div>

        <div>
          <Label htmlFor="current_version">Current Version</Label>
          <Input
            id="current_version"
            {...register('details.current_version')}
            placeholder="v1.0.0"
            className="mt-1"
          />
        </div>
      </div>

      {/* Pricing Details */}
      <div>
        <Label htmlFor="pricing_details">Pricing Details</Label>
        <Textarea
          id="pricing_details"
          {...register('details.pricing_details')}
          placeholder="Detailed pricing information, plans, features included..."
          className="mt-1 min-h-[80px]"
        />
      </div>

      {/* Array Fields - These would need special handling */}
      <div className="space-y-4">
        <div>
          <Label htmlFor="key_features">Key Features (comma-separated)</Label>
          <Textarea
            id="key_features"
            {...register('details.key_features_text')}
            placeholder="AI-powered analysis, Real-time collaboration, Advanced analytics..."
            className="mt-1 min-h-[60px]"
          />
          <p className="text-xs text-gray-500 mt-1">
            Enter features separated by commas. They will be converted to a list.
          </p>
        </div>

        <div>
          <Label htmlFor="use_cases">Use Cases (comma-separated)</Label>
          <Textarea
            id="use_cases"
            {...register('details.use_cases_text')}
            placeholder="Content creation, Data analysis, Customer support..."
            className="mt-1 min-h-[60px]"
          />
          <p className="text-xs text-gray-500 mt-1">
            Enter use cases separated by commas. They will be converted to a list.
          </p>
        </div>

        <div>
          <Label htmlFor="target_audience">Target Audience (comma-separated)</Label>
          <Textarea
            id="target_audience"
            {...register('details.target_audience_text')}
            placeholder="Developers, Marketers, Data scientists..."
            className="mt-1 min-h-[60px]"
          />
          <p className="text-xs text-gray-500 mt-1">
            Enter target audiences separated by commas. They will be converted to a list.
          </p>
        </div>

        <div>
          <Label htmlFor="support_channels">Support Channels (comma-separated)</Label>
          <Textarea
            id="support_channels"
            {...register('details.support_channels_text')}
            placeholder="Email, Live chat, Phone, Documentation..."
            className="mt-1 min-h-[60px]"
          />
          <p className="text-xs text-gray-500 mt-1">
            Enter support channels separated by commas. They will be converted to a list.
          </p>
        </div>
      </div>
    </div>
  );
}
